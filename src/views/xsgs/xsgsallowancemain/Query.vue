<template>
  <div>
    <a-card>
      <a-form-model
        ref="searchForm"
        :model="searchForm"
        :labelCol="{ span: 8 }"
        :wrapperCol="{ span: 16 }"
      >
        <table style="width: 100%; border: 1px solid #f0f0f0">
          <tbody class="ant-table">
            <tr>
              <td>
                <a-form-model-item label="年度" />
              </td>
              <td>
                <a-input-number
                  :min="0"
                  step="1"
                  style="width: 100%"
                  v-model="searchForm.year"
                />
              </td>

              <td>
                <a-form-model-item label="客户号" />
              </td>
              <td>
                <a-input
                  v-model.trim="searchForm.customer"
                  @blur="customerBlurs"
                />
              </td>

              <td>
                <a-form-model-item label="客户名称" />
              </td>
              <td colspan="2">
                <a-select
                  style="min-width: 200px"
                  v-model="searchForm.customerName"
                  show-search
                  :default-active-first-option="false"
                  :show-arrow="false"
                  :allowClear="true"
                  :filter-option="false"
                  :not-found-content="null"
                  @search="handleCustomerSearch"
                  @change="handleCustomerChange"
                >
                  <a-select-option v-for="d in customerList" :key="d.name">
                    {{ d.name }}
                  </a-select-option>
                </a-select>
              </td>

              <td>
                <a-button
                  type="primary"
                  style="margin: 0 8px"
                  @click="handleSearch"
                >
                  <a-icon type="search" />
                  查询
                </a-button>
                <a-button
                  type="primary"
                  style="margin: 0 8px"
                  @click="resetQuery"
                >
                  <a-icon type="reload" />
                  重置
                </a-button>
              </td>
              <td>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? "收起" : "展开" }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a>
              </td>
            </tr>
            <template v-if="advanced">
              <tr>
                <td>
                  <a-form-model-item label="板块" />
                </td>
                <td>
                  <a-select
                    v-model="plate"
                    mode="multiple"
                    style="min-width: 150px"
                  >
                    <a-select-option value="卡车">卡车</a-select-option>
                    <a-select-option value="客车">客车</a-select-option>
                    <a-select-option value="新能源">新能源</a-select-option>
                  </a-select>
                </td>

                <td>
                  <a-form-model-item label="状态机" />
                </td>
                <td>
                  <a-input v-model.trim="searchForm.machineType" />
                </td>

                <td>
                  <a-form-model-item label="产品型号" />
                </td>
                <td>
                  <a-select
                    v-model="productModel"
                    mode="multiple"
                    style="min-width: 150px"
                  >
                    <a-select-option v-for="sty in zcpxh_list" :key="sty">
                      {{ sty }}
                    </a-select-option>
                  </a-select>
                </td>
              </tr>

              <tr>
                <td>
                  <a-form-model-item label="排放" />
                </td>
                <td>
                  <a-select
                    v-model="blowoff"
                    mode="multiple"
                    style="min-width: 150px"
                  >
                    <a-select-option value="国6">国6</a-select-option>
                    <a-select-option value="国5">国5</a-select-option>
                    <a-select-option value="国4">国4</a-select-option>
                    <a-select-option value="国3">国3</a-select-option>
                    <a-select-option value="国2">国2</a-select-option>
                    <a-select-option value="国1">国1</a-select-option>
                  </a-select>
                </td>

                <td>
                  <a-form-model-item label="系列" />
                </td>
                <td>
                  <a-select
                    v-model="series"
                    mode="multiple"
                    style="min-width: 150px"
                  >
                    <a-select-option v-for="sty in zxl_list" :key="sty">
                      {{ sty }}
                    </a-select-option>
                  </a-select>
                </td>

                <td>
                  <a-form-model-item label="功率(PS)" />
                </td>
                <td>
                  <a-select
                    v-model="ps"
                    mode="multiple"
                    style="min-width: 150px"
                  >
                    <a-select-option v-for="sty in zgl_list" :key="sty">
                      {{ sty ? sty.split(".")[0] : "" }}
                    </a-select-option>
                  </a-select>
                </td>
              </tr>
              <tr>
                <td>
                  <a-form-model-item label="品系1" />
                </td>
                <td>
                  <a-select
                    v-model="type"
                    mode="multiple"
                    style="min-width: 150px"
                  >
                    <a-select-option v-for="sty in zpx_list" :key="sty">
                      {{ sty }}
                    </a-select-option>
                  </a-select>
                </td>
                <td>
                  <a-form-model-item label="品系2" />
                </td>
                <td>
                  <a-select
                    v-model="strain1"
                    mode="multiple"
                    style="min-width: 150px"
                  >
                    <a-select-option v-for="sty in strain1List" :key="sty">
                      {{ sty }}
                    </a-select-option>
                  </a-select>
                </td>
                <td>
                  <a-form-model-item label="品系3" />
                </td>
                <td>
                  <a-select
                    v-model="strain2"
                    mode="multiple"
                    style="min-width: 150px"
                  >
                    <a-select-option v-for="sty in strain2List" :key="sty">
                      {{ sty }}
                    </a-select-option>
                  </a-select>
                </td>
                <td>
                  <a-form-model-item label="品系4" />
                </td>
                <td>
                  <a-select
                    v-model="strain3"
                    mode="multiple"
                    style="min-width: 150px"
                  >
                    <a-select-option v-for="sty in strain3List" :key="sty">
                      {{ sty }}
                    </a-select-option>
                  </a-select>
                </td>
              </tr>
            </template>
          </tbody>
        </table>
      </a-form-model>

      <a-tabs default-active-key="1">
        <a-tab-pane key="1" tab="价格信息">
          <vxe-grid
            :scroll-x="{ enabled: false }"
            :scroll-y="{ mode: 'wheel', oSize: 15 }"
            :height="tableScroll"
            stripe
            highlight-hover-row
            highlight-current-row
            highlight-current-column
            @custom="toolbarCustomEvent"
            v-bind="gridOptions"
            @page-change="handlePageChange"
            ref="vxeTable"
            @cell-menu="cellContextMenuEvent"
            @menu-click="contextMenuClickEvent"
            :show-header="showHeader"
            :span-method="colspanMethod"
            :row-style="rowStyle"
            :cell-style="cellStyle"
            :seq-config="{
              startIndex:
                (gridOptions.pagerConfig.currentPage - 1) *
                gridOptions.pagerConfig.pageSize,
            }"
          >
            <template #toolbar_buttons>
              <vxe-span>{{ customerStr }}</vxe-span>
            </template>
            <template #action="{ row }">
              <a @click="onclick(row)">{{ row.platformName }}</a>
            </template>
            <template #defaultRemarks="{ row, column }">
              <!-- <vxe-span>{{ row.tcRemarks }}</vxe-span> -->
              <template v-if="row[column['field']]">
                <a-tooltip placement="left">
                  <template slot="title">
                    {{ row[column["field"]] }}
                  </template>
                  {{ row[column["field"]] }}
                </a-tooltip>
              </template>
              <template v-else>
                {{ row[column["field"]] }}
              </template>
            </template>
            <template slot="ps" slot-scope="{ row }">
              {{ row.ps ? row.ps.split(".")[0] : "" }}
            </template>
            <template slot="contract" slot-scope="{ row }">
              {{ row.contract ? row.contract : "未签" }}
            </template>
          </vxe-grid>
        </a-tab-pane>
        <a-tab-pane key="2" tab="推广信息" force-render>
          <a-tabs default-active-key="1">
            <a-tab-pane key="1" tab="本年度推广">
              <vxe-toolbar>
                <template #buttons>
                  <vxe-button
                    v-if="PF_LIST.includes('PRICE_SEARCH_EXPORT')"
                    status="primary"
                    size="medium"
                    @click="exportDataEvent"
                  >
                    <a-icon type="download" />
                    导出
                  </vxe-button>
                </template>
              </vxe-toolbar>
              <vxe-grid
                :tooltip-config="{ enterable: true, showAll: true }"
                v-bind="gridOptionsTG"
                ref="xTable1"
                @page-change="handlePageChangeTG"
              ></vxe-grid>
            </a-tab-pane>
            <a-tab-pane key="2" tab="上一年度推广" force-render>
              <vxe-grid
                v-bind="gridOptionsTGS"
                ref="xTable2"
                @page-change="handlePageChangeTGS"
              ></vxe-grid>
            </a-tab-pane>
          </a-tabs>
        </a-tab-pane>
      </a-tabs>
    </a-card>

    <a-modal
      :title="platformName1 + '样例'"
      :visible="lookAllDialog"
      :confirm-loading="confirmAllLoading"
      width="75%"
      :zIndex="1088"
      @ok="handlelookAllSubmit"
      @cancel="closelookAllForm"
    >
      <a-form-model>
        <selectAllOptionalList
          :record="records"
          ref="selectAllOptionalList"
        ></selectAllOptionalList>
      </a-form-model>
    </a-modal>
  </div>
</template>

<script>
import { hasAuth } from "@/utils/authority-utils";
import {
  LIST_XSGSALLOWANCEITEM_PAGE,
  GETSEACHLIST_XSGSALLOWANCEITEM,
  LIST_XSGSFEEPROMOTE_PAGE,
  LIST_XSGSCUSTOMER,
  PARTS_NAMES,
  GET_XSGSREPORTTHEME_USERID,
  SUBMIT_XSGSREPORTTHEME,
  LIST_XSGSREPORTCOLUMNS,
  LIST_MAINSUBCUST,
  DETAIL_XSGSALLOWANCEITEM,
} from "@/services/api/xsgs";
import { METHOD, request } from "@/utils/request";
import Cookie from "js-cookie";
import {
  SetCustomFields,
  ColumnCustomRender,
  groupDataByFieldName,
  sendThis,
} from "@/utils/data-group";
import selectAllOptionalList from "./select_allOptionalList";

export default {
  name: "Query.vue",
  components: {
    selectAllOptionalList,
  },
  data() {
    let year = new Date().getFullYear();

    return {
      hasAuth,
      Cookie,
      loading: false,
      lookAllDialog: false,
      confirmAllLoading: false,
      dialog: false,
      confirmLoading: false,
      // 高级搜索 展开/关闭
      advanced: false,
      key: null,
      a: null,
      platformName1: null,
      records: null,
      customerStr: null,
      //客户
      mainData: [],
      fullPath: null,
      searchForm: {
        year: null,
        customerName: null,
        customer: null,
      },
      searchForms: {
        year: null,
        customerName: null,
        customer: null,
      },

      productModel: [],
      machineType: [],
      blowoff: [],
      plate: [],
      series: [],
      ps: [],
      type: [],
      strain1: [],
      strain2: [],
      strain3: [],

      zcpxh_list: [], //产品型号
      zztj_list: [], //状态机
      zxl_list: [], //系列
      zgl_list: [], //功率
      zpx_list: [], //品系
      strain1List: [], //品系2
      strain2List: [], //品系3
      strain3List: [], //品系4
      customerList: [],
      tableScroll: null,
      TF_FIELD: "PRICE_LIBRARY_SEARCH,PRICE_SEARCH,PRICE_SEARCH_TF",
      PF_FIELD: "PRICE_LIBRARY_SEARCH,PRICE_SEARCH,PRICE_SEARCH_PF",
      TF_FIELD_PR: "PRICE_LIBRARY_SEARCH,PRICE_SEARCH,PRICE_SEARCH_PROMOTE",
      PF_LIST: [],
      form: {},
      mergeCells: [],
      showHeader: false,

      menuConfig: {
        body: {
          options: [
            [
              {
                code: "edit",
                name: "编辑",
                prefixIcon: "fa fa-edit",
                visible: true,
                disabled: false,
              },
              {
                code: "delete",
                name: "删除",
                prefixIcon: "fa fa-edit",
                visible: true,
                disabled: false,
              },
              {
                code: "copy",
                name: "参考",
                prefixIcon: "fa fa-edit",
                visible: true,
                disabled: false,
              },
            ],
            [
              {
                code: "compare",
                name: "选配件价格对比",
                prefixIcon: "fa fa-edit",
                visible: true,
                disabled: false,
              },
            ],
          ],
        },
      },
      gridOptions: {
        border: true,
        resizable: true,
        keepSource: true,
        showOverflow: true,
        id: "vxe-table",
        rowId: "id",
        loading: false,
        pagerConfig: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
          pageSizes: [10, 20, 50, { label: "显示全部", value: -1 }],
          layouts: [
            "Sizes",
            "PrevJump",
            "PrevPage",
            "Number",
            "NextPage",
            "NextJump",
            "FullJump",
            "Total",
          ],
          perfect: true,
        },
        toolbarConfig: {
          //导入
          import: false,
          //导出
          export: true,
          //打印
          print: false,
          //放大
          zoom: true,
          //筛选框
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        radioConfig: {
          labelField: "id",
          reserve: true,
          highlight: true,
        },
        zoomConfig: {
          style: {
            zIndex: "666",
          },
        },
        //导入参数
        importConfig: {
          remote: true,
          importMethod: this.importMethod,
          types: ["xlsx"],
          modes: ["insert"],
        },
        menuConfig: {},
        //导出参数
        exportConfig: {
          // 默认选中类型
          type: "xlsx",
          filename: "价格信息",
          sheetName: "Sheet1",
          //默认选中分组
          isColgroup: true,
          //默认选中样式
          useStyle: true,
          original: false,
          // 局部自定义类型
          types: ["xlsx"],
          // 自定义数据量列表
          modes: ["current"],
        },
        // 分组列头，通过 children 定义子列
        columns: [],
        data: [],
      },
      columns: [],
      initColumns: [
        {
          title: "序号",
          align: "center",
          type: "seq",
          width: 40,
          fixed: "left",
          headerClassName: "col-basic",
          exclude: true,
        },
        {
          title: "基础数据",
          align: "center",
          field: "col_0",
          fixed: "left",
          headerClassName: "col-basic",
          children: [
            //{title: '核算单号', field: 'masterId',width: 110, align: 'center'},
            {
              title: "年度",
              field: "year",
              width: 38,
              headerClassName: "col-basic",
              align: "center",
            },
            {
              title: "客户号",
              headerClassName: "col-basic",
              field: "customer",
              minWidth: 50,
              align: "center",
              scopedSlots: { customRender: "customer" },
            },
            {
              title: "客户",
              field: "customerName",
              minWidth: 150,
              headerClassName: "col-basic",
              align: "center",
            },

            {
              title: "合同号",
              field: "contract",
              width: 70,
              align: "center",
              headerClassName: "col-basic",
              slots: { default: "contract" },
            },
            {
              title: "客户物料",
              field: "materialNo",
              width: 70,
              headerClassName: "col-basic",
              align: "center",
            },
            {
              title: "板块",
              field: "plate",
              width: 40,
              headerClassName: "col-basic",
              align: "center",
            },
            {
              title: "排放",
              field: "blowoff",
              width: 40,
              headerClassName: "col-basic",
              align: "center",
            },
            {
              title: "系列",
              field: "series",
              width: 40,
              headerClassName: "col-basic",
              align: "center",
            },
            {
              title: "产品型号",
              width: 100,
              field: "productModel",
              headerClassName: "col-basic",
              align: "center",
            },
            {
              title: "功率(PS)",
              field: "ps",
              width: 40,
              align: "center",
              headerClassName: "col-basic",
              slots: { default: "ps" },
            },
            {
              title: "状态机",
              field: "platformName",
              width: 90,
              align: "center",
              headerClassName: "col-basic",
              slots: { default: "action" },
            },
            {
              title: "品系1",
              field: "type",
              minWidth: 48,
              headerClassName: "col-basic",
              align: "center",
            },
            {
              title: "品系2",
              field: "strain1",
              minWidth: 48,
              headerClassName: "col-basic",
              align: "center",
            },
            {
              title: "品系3",
              field: "strain2",
              minWidth: 80,
              headerClassName: "col-basic",
              align: "center",
            },
            {
              title: "品系4",
              field: "strain3",
              minWidth: 100,
              headerClassName: "col-basic",
              align: "center",
            },
            {
              title: "前年销量",
              field: "yearSale1",
              minWidth: 48,
              headerClassName: "col-basic",
              align: "center",
            },
            {
              title: "去年销量",
              field: "yearSale2",
              minWidth: 48,
              headerClassName: "col-basic",
              align: "center",
            },
            {
              title: "今年销量",
              field: "yearSale3",
              minWidth: 48,
              headerClassName: "col-basic",
              align: "center",
            },
            /*                            {title: '年度销售预计', dataIndex: 'yearExpectVolume',width: 65, align: 'center'},
                                        {title: '年度实际销售', dataIndex: 'yearSalesVolume',width: 65, align: 'center'},
                                        {title: '基本型预算', dataIndex: 'basicBudgetPrice',width: 65, align: 'center'},*/
          ],
        },
        {
          title: "细分市场",
          key: "subMarket",
          field: "marketSegment",
          align: "center",
          headerAlign: "center",
          headerClassName: "col-lastYear",
          children: [
            {
              title: "BU业务",
              key: "zzbuyw",
              field: "zzbuyw",
              width: 70,
              align: "center",
              headerClassName: "col-lastYear",
            },
            {
              title: "品系",
              key: "zzpx",
              field: "zzpx",
              width: 70,
              align: "center",
              headerClassName: "col-lastYear",
            },
            {
              title: "场景1",
              key: "zzxcj1",
              field: "zzxcj1",
              width: 70,
              align: "center",
              headerClassName: "col-lastYear",
            },
            {
              title: "场景2",
              key: "zzxcj2",
              field: "zzxcj2",
              width: 70,
              align: "center",
              headerClassName: "col-lastYear",
            },
            {
              title: "场景3",
              key: "zzxcj3",
              field: "zzxcj3",
              width: 70,
              align: "center",
              headerClassName: "col-lastYear",
            },
          ],
        },
        // 增加销量板块 ，子项23年销量	24年销量	25年销量	25年预算销量	25年客户预判销量	25年销量差额

        {
          title: "销量",
          align: "center",
          field: "salesVolume",
          headerClassName: "col-xz",
          children: [
            {
              title: "前年销量",
              field: "yearSale1",
              width: 70,
              align: "center",
              headerClassName: "col-xz",
            },
            {
              title: "去年销量",
              field: "yearSale2",
              width: 70,
              align: "center",
              headerClassName: "col-xz",
            },
            {
              title: "今年销量",
              field: "yearSale3",
              width: 70,
              align: "center",
              headerClassName: "col-xz",
            },
            {
              title: `今年预判销量`,
              field: "yearSale5",
              width: 70,
              align: "center",
              headerClassName: "col-xz",
            },
            {
              title: `今年预算销量`,
              field: "yearSale4",
              width: 70,
              align: "center",
              headerClassName: "col-xz",
            },
            {
              title: `今年销量差额`,
              field: "yearSale6",
              width: 70,
              align: "center",
              headerClassName: "col-xz",
            },
          ],
        },
        // 增加  整机成本，子项为24年基准成本	24年实际成本	25年基准成本	25年实际成本；
        {
          title: "整机成本",
          field: "wholeMachineCost",
          align: "center",
          headerClassName: "col-blue",
          children: [
            {
              title: "去年基准成本",
              field: "yearCost1",
              width: 70,
              align: "center",
              headerClassName: "col-blue",
            },
            {
              title: "去年实际成本",
              field: "yearCost2",
              width: 70,
              align: "center",
              headerClassName: "col-blue",
            },
            {
              title: "今年基准成本",
              field: "yearCost3",
              width: 70,
              align: "center",
              headerClassName: "col-blue",
            },
            {
              title: "今年实际成本",
              field: "yearCost4",
              width: 70,
              align: "center",
              headerClassName: "col-blue",
            },
          ],
        },
        // 增加 上一年度价格信息，子项 24年整机开票价	24年基本型开票价	24年整机实际价格	24年基本型实际价格	24年实际价格边贡率	24年选配件合计	24年结算价
        {
          title: "上一年度价格信息",
          align: "center",
          field: "lastYearTitle",
          headerClassName: "col-lastYear",
          children: [
            {
              title: "基本型开票价",
              field: "synBasicTicketPrice",
              width: 70,
              align: "center",
              headerClassName: "col-lastYear",
            },
            {
              title: "整机开票价",
              field: "synUnitTicketPrice",
              width: 70,
              align: "center",
              headerClassName: "col-lastYear",
            },
            {
              title: "基本型实际价格",
              field: "synBasicNetPrice",
              width: 80,
              align: "center",
              headerClassName: "col-lastYear",
            },
            {
              title: "整机实际价格",
              field: "synUnitNetPrice",
              width: 70,
              align: "center",
              headerClassName: "col-lastYear",
            },
            {
              title: "基本型实际价格",
              field: "synBasicNetPrice",
              width: 90,
              align: "center",
              headerClassName: "col-lastYear",
            },
            {
              title: "实际价格边贡率",
              field: "synNetPriceRate",
              width: 90,
              align: "center",
              headerClassName: "col-lastYear",
            },
            {
              title: "选配件合计",
              field: "synOptionAmount",
              width: 60,
              align: "center",
              headerClassName: "col-lastYear",
            },
            {
              title: "结算价",
              field: "synSettlementPrice",
              width: 70,
              align: "center",
              headerClassName: "col-lastYear",
            },
          ],
        },
        {
          title: "上一年度商务信息",
          align: "center",
          field: "lastYearBusiness",
          headerClassName: "col-pre-fjt",
          children: [
            {
              title: "阶梯折让",
              align: "center",
              field: "prevJtZrId",
              headerClassName: "col-pre-fjt",
              width: 100,
              children: [],
            },
            {
              title: "非阶梯折让",
              align: "center",
              field: "prevZrId",
              headerClassName: "col-pre-fjt",
              width: 100,
              children: [
                {
                  title: "备注",
                  field: "remarks",
                  align: "center",
                  width: 80,
                  headerClassName: "col-pre-fjt",
                  ellipsis: true,
                },
              ],
            },

            {
              title: "单台折让合计",
              field: "synBalanceAmount",
              width: 70,
              align: "center",
              headerClassName: "col-pre-fjt",
            },
            {
              title: "24年主机厂推广1",
              field: "yearHostFactoryPromotion1",
              width: 70,
              align: "center",
              headerClassName: "col-pre-fjt",
            },
            {
              title: "24年整机净价",
              field: "yearUnitNetPrice",
              width: 70,
              align: "center",
              headerClassName: "col-pre-fjt",
            },
            {
              title: "24年整机净价边贡率",
              field: "yearUnitNetPriceRate",
              width: 70,
              align: "center",
              headerClassName: "col-pre-fjt",
            },
            {
              title: "去年单台加权平均推广(不含打包)",
              field: "unitPromoNoBundle",
              width: 70,
              align: "center",
              headerClassName: "col-pre-fjt",
            },
            {
              title: "去年单台推广分摊（打包分摊）",
              field: "unitPromoAllocBundle",
              width: 70,
              align: "center",
              headerClassName: "col-pre-fjt",
            },
            {
              title: "去年单台推广分摊合计",
              field: "unitPromoAllocTotal",
              width: 70,
              align: "center",
              headerClassName: "col-pre-fjt",
            },
            {
              title: "去年分摊后整机净价",
              field: "finalUnitNetPrice",
              width: 70,
              align: "center",
              headerClassName: "col-pre-fjt",
            },
          ],
        },

        {
          title: "本年度价格信息",
          align: "center",
          field: "thisYearTitle",
          headerClassName: "col-blue",
          children: [
            {
              title: "降价",
              field: "reducePrice",
              width: 50,
              align: "center",
              headerClassName: "col-blue",
            },
            {
              title: "基本型开票价",
              field: "basicTicketPrice",
              width: 70,
              align: "center",
              headerClassName: "col-blue",
            },
            {
              title: "整机开票价",
              field: "unitTicketPrice",
              width: 70,
              align: "center",
              headerClassName: "col-blue",
            },
            {
              title: "整机实际价格",
              field: "unitNetPrice",
              width: 70,
              align: "center",
              headerClassName: "col-blue",
            },
            {
              title: "25年基本型实际价格",
              field: "basicNetPrice",
              width: 70,
              align: "center",
              headerClassName: "col-blue",
            },
            {
              title: "25年实际价格边贡率",
              field: "budgetNetPriceCmr",
              width: 70,
              align: "center",
              headerClassName: "col-blue",
            },
            {
              title: "结算价",
              field: "settlementPrice",
              width: 80,
              align: "center",
              headerClassName: "col-blue",
            },
            /*            {title: '选配件合计', field: 'optionAmount',width: 165, align: 'center'},
                          {title: '折让合计', field: 'balanceAmount',width: 165, align: 'center'},*/
          ],
        },
        {
          title: "本年度折让信息",
          align: "center",
          field: "yearTitle",
          headerClassName: "col-blue",
          children: [
            {
              title: "阶梯折让",
              align: "center",
              field: "jtzrId",
              headerClassName: "col-blue",
              children: [],
            },
            {
              title: "非阶梯折让",
              align: "center",
              field: "zrId",
              headerClassName: "col-blue",
              children: [],
            },

            {
              title: "25年单台折让合计",
              align: "center",
              field: "balanceAmount",
              width: 70,
              headerClassName: "col-blue",
            },
            {
              title: "25年主机厂推广1",
              align: "center",
              field: "yearHostFactoryPromotion1",
              width: 70,
              headerClassName: "col-blue",
            },
            {
              title: "25年整机净价",
              align: "center",
              field: "yearUnitNetPrice",
              width: 70,
              headerClassName: "col-blue",
            },
            {
              title: "25年整机净价边贡率",
              align: "center",
              field: "yearUnitNetPriceRate",
              width: 70,
              headerClassName: "col-blue",
            },
            {
              title: "25年单台加权平均推广(不含打包)",
              align: "center",
              field: "unitPromoNoBundle",
              width: 70,
              headerClassName: "col-blue",
            },
            {
              title: "25年单台推广分摊（打包分摊）",
              align: "center",
              field: "unitPromoAllocBundle",
              width: 70,
              headerClassName: "col-blue",
            },
            {
              title: "25年单台推广分摊合计",
              align: "center",
              field: "unitPromoAllocTotal",
              width: 70,
              headerClassName: "col-blue",
            },
            {
              title: "25年分摊后整机净价",
              align: "center",
              field: "finalUnitNetPrice",
              width: 70,
              headerClassName: "col-blue",
            },
          ],
        },
        {
          title: "预算价格",
          align: "center",
          field: "yearTitle",
          headerClassName: "col-zz",
          children: [
            {
              title: "25年预算价格降价",
              align: "center",
              field: "budgetPriceReduction",
              width: 70,
              headerClassName: "col-zz",
            },
            {
              title: "25年基本型预算价格",
              align: "center",
              field: "baseModelBudgetPrice",
              width: 70,
              headerClassName: "col-zz",
            },
            {
              title: "25年预算价格",
              align: "center",
              field: "currentYearBudgetPrice",
              width: 70,
              headerClassName: "col-zz",
            },
            {
              title: "25年预算主机厂推广",
              align: "center",
              field: "budgetOemPromotion",
              width: 70,
              headerClassName: "col-zz",
            },
            {
              title: "25年预算净价",
              align: "center",
              field: "budgetNetPrice",
              width: 70,
              headerClassName: "col-zz",
            },
            {
              title: "25年单台预算政策合计",
              align: "center",
              field: "unitBudgetPolicyTotal",
              width: 70,
              headerClassName: "col-zz",
            },
            // 25年预算价格结余	25年预算净价边贡率
            {
              title: "25年预算价格结余",
              align: "center",
              field: "budgetPriceSurplus",
              width: 70,
              headerClassName: "col-zz",
            },
            {
              title: "25年预算净价边贡率",
              align: "center",
              field: "budgetNetPriceCmr",
              width: 70,
              headerClassName: "col-zz",
            },
          ],
        },
        {
          title: "收入",
          align: "center",
          field: "income",
          headerClassName: "col-bg",
          children: [
            {
              title: "去年净收入(万元)",
              field: "lastYearNetIncome",
              align: "center",
              width: 70,
              headerClassName: "col-bg",
            },
            {
              title: "今年预算净收入(万元)",
              field: "budgetedNetIncome",
              align: "center",
              width: 70,
              headerClassName: "col-bg",
            },
            {
              title: "今年实际价格收入(万元)",
              field: "actualPriceRevenue",
              align: "center",
              width: 70,
              headerClassName: "col-bg",
            },
            {
              title: "今年实际推广金额(万元)",
              field: "actualPromoAmount",
              align: "center",
              width: 70,
              headerClassName: "col-bg",
            },
            {
              title: "今年实际净收入(万元)",
              field: "actualNetIncome",
              align: "center",
              width: 70,
              headerClassName: "col-bg",
            },
            {
              title: "今年收入差额(万元)",
              field: "revenueVariance",
              align: "center",
              width: 70,
              headerClassName: "col-bg",
            },
          ],
        },
        {
          title: "边贡额",
          align: "center",
          field: "borderTributeAmount",
          headerClassName: "col-bg",
          children: [
            // contributionMarginAmount	去年边贡额(万元)
            {
              title: "去年边贡额(万元)",
              field: "contributionMarginAmount",
              align: "center",
              width: 70,
              headerClassName: "col-bg",
            },
            // budgetedContributionMarginAmount	今年预算边贡额(万元)
            {
              title: "今年预算边贡额(万元)",
              field: "budgetedContributionMarginAmount",
              align: "center",
              width: 70,
              headerClassName: "col-bg",
            },
            // actualContributionMarginAmount	今年实际边贡额(万元)
            {
              title: "今年实际边贡额(万元)",
              field: "actualContributionMarginAmount",
              align: "center",
              width: 70,
              headerClassName: "col-bg",
            },
            // contributionMarginVariance	今年边贡差额(万元)
            {
              title: "今年边贡差额(万元)",
              field: "contributionMarginVariance",
              align: "center",
              width: 70,
              headerClassName: "col-bg",
            },
          ],
        },
        // {
        //   title: "边贡信息",
        //   align: "center",
        //   headerClassName: "col-bg",
        //   field: "bg",
        //   children: [
        //     {
        //       title: "基准成本",
        //       field: "baseCost",
        //       align: "center",
        //       headerClassName: "col-bg",
        //       children: [
        //         {
        //           title: "材料成本",
        //           field: "baseCostOfMeterial",
        //           width: 70,
        //           align: "center",
        //           headerClassName: "col-bg",
        //         },
        //         {
        //           title: "边际贡献",
        //           field: "baseContribution",
        //           width: 70,
        //           align: "center",
        //           headerClassName: "col-bg",
        //         },
        //         {
        //           title: "边际贡献率(%)",
        //           field: "baseContributionPercent",
        //           width: 70,
        //           align: "center",
        //           headerClassName: "col-bg",
        //         },
        //       ],
        //     },
        //     {
        //       title: "实际成本",
        //       field: "meterialCost",
        //       align: "center",
        //       headerClassName: "col-bg",
        //       children: [
        //         {
        //           title: "材料成本",
        //           field: "costOfMeterial",
        //           width: 70,
        //           align: "center",
        //           headerClassName: "col-bg",
        //         },
        //         {
        //           title: "边际贡献",
        //           field: "contribution",
        //           width: 70,
        //           align: "center",
        //           headerClassName: "col-bg",
        //         },
        //         {
        //           title: "边际贡献率(%)",
        //           field: "contributionPercent",
        //           width: 70,
        //           align: "center",
        //           headerClassName: "col-bg",
        //         },
        //       ],
        //     },
        //   ],
        // },

        {
          title: "选配件降价合计",
          align: "center",
          field: "optionReducePrice",
          width: 60,
          headerClassName: "col-xx",
        },
        {
          title: "选配件合计",
          align: "center",
          field: "optionAmount",
          width: 70,
          headerClassName: "col-xx",
        },
        {
          title: "选型件",
          field: "basicList",
          align: "center",
          headerClassName: "col-xx-h",
          children: [],
        },
        {
          title: "选装件",
          field: "optionListColumns",
          align: "center",
          headerClassName: "col-xz-h",
          children: [],
        },
        {
          title: "剔除金额",
          field: "rejectAmount",
          align: "center",
          width: 80,
          headerClassName: "col-je",
        },
        {
          title: "备注",
          field: "remarks",
          align: "center",
          width: 80,
          headerClassName: "col-bz",
          ellipsis: true,
        },
      ],
      visibleColumn: null,
      data: [],
      prev_jts: [],
      prev_no_jts: [],
      jts: [],
      no_jts: [],
      xxColumns: [],
      xzColumns: [],
      gridOptionsTG: {
        border: true,
        resizable: true,
        keepSource: true,
        showOverflow: true,
        loading: false,
        pagerConfig: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
          pageSizes: [10, 20, 50],
          layouts: [
            "Sizes",
            "PrevJump",
            "PrevPage",
            "Number",
            "NextPage",
            "NextJump",
            "FullJump",
            "Total",
          ],
          perfect: true,
        },
        columns: [],
        initColumns: [
          {
            title: "序号",
            align: "center",
            type: "seq",
            width: 50,
            fixed: "left",
            exclude: true,
          },
          { title: "年度", field: "year", width: 95 },
          { title: "推广名称", field: "tgName", width: 95 },
          { title: "板块", field: "plate", width: 95 },
          { title: "客户号", field: "customer", width: 95 },
          { title: "客户名称", field: "customerName", width: 95 },
          { title: "客户简称", field: "customerJc", width: 95 },
          { title: "产品归属", field: "gsk6", width: 95 },
          { title: "燃料", field: "fuel", width: 95 },
          { title: "系列", field: "series", width: 95 },
          { title: "排放", field: "blowoff", width: 95 },
          {
            title: "产品型号",
            field: "productModel",
            ellipsis: true,
            width: 95,
          },
          { title: "状态机", field: "platformName", width: 95 },
          { title: "功率", field: "power", width: 95 },
          { title: "推广区域", field: "area", width: 95 },
          { title: "品系", field: "type", width: 95 },
          { title: "车型", field: "cx", width: 95 },
          { title: "单价", field: "limitPrice", width: 95 },
          { title: "数量", field: "limitNumber", width: 95 },
          { title: "金额", field: "limitAmount", width: 95 },
          {
            title: "其他推广备注",
            field: "remarks",
            ellipsis: true,
            width: 95,
          },
          { title: "打包推广总额", field: "total", width: 95 },
          { title: "打包推广条件", field: "condition", width: 95 },
          { title: "推广开始时间", field: "startTime", width: 95 },
          { title: "推广结束时间", field: "endTime", width: 95 },
          { title: "实结数量", field: "realNumber", width: 95 },
          { title: "实结金额", field: "realAmount", width: 95 },
          { title: "系列开票数量", field: "number", width: 95 },
          { title: "单台推广分摊", field: "dttgft", width: 95 },
          { title: "是否已编制协议", field: "isBz", width: 120 },
          { title: "协议是否已送审", field: "isSs", width: 120 },
        ],
        data: [],
      },
      gridOptionsTGS: {
        border: true,
        resizable: true,
        keepSource: true,
        showOverflow: true,
        loading: false,
        pagerConfig: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
          pageSizes: [10, 20, 50],
          layouts: [
            "Sizes",
            "PrevJump",
            "PrevPage",
            "Number",
            "NextPage",
            "NextJump",
            "FullJump",
            "Total",
          ],
          perfect: true,
        },
        columns: [],
        initColumns: [
          {
            title: "序号",
            align: "center",
            type: "seq",
            width: 50,
            fixed: "left",
            exclude: true,
          },
          { title: "年度", field: "year", width: 95 },
          { title: "推广名称", field: "tgName", width: 95 },
          { title: "板块", field: "plate", width: 95 },
          { title: "客户号", field: "customer", width: 95 },
          { title: "客户名称", field: "customerName", width: 95 },
          { title: "客户简称", field: "customerJc", width: 95 },
          { title: "产品归属", field: "gsk6", width: 95 },
          { title: "燃料", field: "fuel", width: 95 },
          { title: "系列", field: "series", width: 95 },
          { title: "排放", field: "blowoff", width: 95 },
          {
            title: "产品型号",
            field: "productModel",
            ellipsis: true,
            width: 95,
          },
          { title: "状态机", field: "platformName", width: 95 },
          { title: "功率", field: "power", width: 95 },
          { title: "推广区域", field: "area", width: 95 },
          { title: "品系", field: "type", width: 95 },
          { title: "车型", field: "cx", width: 95 },
          { title: "单价", field: "limitPrice", width: 95 },
          { title: "数量", field: "limitNumber", width: 95 },
          { title: "金额", field: "limitAmount", width: 95 },
          {
            title: "其他推广备注",
            field: "remarks",
            ellipsis: true,
            width: 95,
          },
          { title: "打包推广总额", field: "total", width: 95 },
          { title: "打包推广条件", field: "condition", width: 95 },
          { title: "推广开始时间", field: "startTime", width: 95 },
          { title: "推广结束时间", field: "endTime", width: 95 },
          { title: "实结数量", field: "realNumber", width: 95 },
          { title: "实结金额", field: "realAmount", width: 95 },
          { title: "系列开票数量", field: "number", width: 95 },
          { title: "单台推广分摊", field: "dttgft", width: 95 },
          { title: "是否已编制协议", field: "isBz", width: 120 },
          { title: "协议是否已送审", field: "isSs", width: 120 },
        ],
        data: [],
      },
      baseAuthColumns: [],
      tooltipConfig: {
        showAll: true,
        enterable: true,
        contentMethod: ({ column, row, _columnIndex }) => {
          const { field } = column;
          // 重写默认的提示内容
          if (field.includes("remarks") || field === "rejectAmount") {
            // console.log(row.tcRemarks || row[field]);

            return row.tcRemarks || row[field];
          }
          // 其余的单元格使用默认行为
          return null;
        },
      },
    };
  },
  //获取当前年份
  async created() {
    var aData = new Date();
    this.searchForm.year = Number(this.$moment(aData).format("YYYY"));
    this.yearchanged();
    // 获取页面功能权限
    await this.getUserAuth(this.PF_FIELD);
    this.PF_LIST = this.getPfList(this.PF_FIELD);
    this.coverAuth();
  },
  async mounted() {
    // 获取价格查询字段权限
    await this.getUserAuth(this.TF_FIELD);
    this.columns = this.coverAuthColumn(
      this.TF_FIELD,
      this.initColumns,
      "field"
    );

    // 获取推广信息字段权限
    await this.getUserAuth(this.TF_FIELD_PR);
    this.gridOptionsTG.columns = this.coverAuthColumn(
      this.TF_FIELD_PR,
      this.gridOptionsTG.initColumns,
      "field"
    );
    this.gridOptionsTGS.columns = this.coverAuthColumn(
      this.TF_FIELD_PR,
      this.gridOptionsTGS.initColumns,
      "field"
    );

    this.fullPath = this.$route.fullPath;
    SetCustomFields(["platformName"]);
    this.partsNames();
    // this.getData();
    this.loadColum(null, 0);
    //发送this 到 js 文件里
    sendThis(this);
    this.getTgxxData();
    this.getSearchList();
    this.checkName();
    this.calcPageHeight();
  },
  methods: {
    coverAuth() {
      // PRICE_SEARCH_EDIT  PRICE_SEARCH_REF PRICE_SEARCH_COMPARE
      // 右键菜单，导出按钮
      if (!this.PF_LIST) {
        this.gridOptions.menuConfig = {};
        this.gridOptions.toolbarConfig.export = false;
      } else {
        let that = this;
        if (!this.PF_LIST.includes("PRICE_SEARCH_EXPORT")) {
          this.gridOptions.toolbarConfig.export = false;
        }
        this.menuConfig.body.options.forEach((s) => {
          s.forEach((item) => {
            if (item.code == "edit") {
              if (!this.PF_LIST.includes("PRICE_SEARCH_EDIT")) {
                item["visible"] = false;
              }
            } else if (item.code == "copy") {
              if (!this.PF_LIST.includes("PRICE_SEARCH_REF")) {
                item["visible"] = false;
              }
            } else if (item.code == "compare") {
              if (!this.PF_LIST.includes("PRICE_SEARCH_COMPARE")) {
                item["visible"] = false;
              }
            }
          });
        });
        this.gridOptions.menuConfig = this.menuConfig;
      }
    },
    getSearchList() {
      request(GETSEACHLIST_XSGSALLOWANCEITEM, METHOD.GET).then((res) => {
        if (res.data.data.cpxh != null) {
          this.zcpxh_list = res.data.data.cpxh;
        }
        if (res.data.data.ztj != null) {
          this.zztj_list = res.data.data.ztj;
        }
        if (res.data.data.xl != null) {
          this.zxl_list = res.data.data.xl;
        }
        if (res.data.data.gl != null) {
          this.zgl_list = res.data.data.gl;
        }
        if (res.data.data.px != null) {
          this.zpx_list = res.data.data.px;
        }
        if (res.data.data.px != null) {
          this.strain1List = res.data.data.strain1;
        }
        if (res.data.data.px != null) {
          this.strain2List = res.data.data.strain2;
        }
        if (res.data.data.px != null) {
          this.strain3List = res.data.data.strain3;
        }
      });
    },

    customerBlurs() {
      if (!this.searchForm.customer?.trim()) {
        console.log("客户号为空，跳过查询");
        return;
      }

      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        customer: this.searchForm.customer,
      }).then((res) => {
        if (res.data.data.length > 0) {
          if (this.searchForm.customer != null) {
            if (!this.searchForm.customer.split(" ").join("").length == 0) {
              this.searchForm.customerName = res.data.data[0].name;
            }
            this.setTableCustomer();
          }
        } else {
          this.$message.info("此客户号没找到信息");
        }
      });
    },

    handleCustomerSearch(value) {
      request(LIST_XSGSCUSTOMER, METHOD.POST, {
        name: value,
      }).then((res) => {
        this.customerList = res.data.data;
      });
    },
    handleCustomerChange(value) {
      this.customerList.forEach((item) => {
        if (item.name == value) {
          this.searchForm.customer = item.customer;
          this.setTableCustomer();
        }
      });
    },

    calcPageHeight() {
      let timeHandler = setInterval(() => {
        let content = document.getElementsByClassName(
          "admin-layout-main beauty-scroll ant-layout"
        )[0].offsetHeight;

        let Header = document.getElementsByClassName(
          "ant-layout-header light admin-header multi-page"
        )[0].offsetHeight;
        let searchBtn = document.getElementsByClassName(
          "ant-form ant-form-horizontal"
        )[0].offsetHeight;
        let tableHeader = document.getElementsByClassName(
          "tabs-head side fixed"
        )[0].offsetHeight;
        let containeHeight = document.getElementsByClassName(
          "ant-tabs-nav-container"
        )[0].offsetHeight;
        let vxeToolbar =
          document.getElementsByClassName("vxe-toolbar")[0].offsetHeight;

        this.tableScroll =
          content -
          Header -
          searchBtn -
          tableHeader -
          containeHeight -
          vxeToolbar;
      }, 50);

      setTimeout(() => {
        clearInterval(timeHandler);
      }, 1000);
    },

    getData() {
      this.gridOptions.loading = true;
      let current = this.gridOptions.pagerConfig.currentPage;
      let size = this.gridOptions.pagerConfig.pageSize;
      if (this.searchForm.year === null) {
        var aData = new Date();
        this.searchForm.year = Number(this.$moment(aData).format("YYYY"));
      }
      this.searchForm.productModels = this.productModel.join(",");
      this.searchForm.machineTypes = this.machineType.join(",");
      this.searchForm.blowoffs = this.blowoff.join(",");
      this.searchForm.plates = this.plate.join(",");
      this.searchForm.seriess = this.series.join(",");
      this.searchForm.pss = this.ps.join(",");
      this.searchForm.types = this.type.join(",");
      this.searchForm.strain1s = this.strain1.join(",");
      this.searchForm.strain2s = this.strain2.join(",");
      this.searchForm.strain3s = this.strain3.join(",");

      request(LIST_XSGSALLOWANCEITEM_PAGE, METHOD.GET, {
        ...this.searchForm,
        current,
        size,
      }).then((res) => {
        this.$nextTick(function () {
          const { records, total } = res.data.data;
          this.yearchanged();
          this.setItemDatas(records);
          this.loadColum(records, total);
        });
      });
    },

    async loadColum(records, total) {
      this.showHeader = true;
      await this.$nextTick();
      if (this.visibleColumn == null) {
        this.$refs.vxeTable.loadColumn(this.columns); //操作a组件
      }
      if (
        this.searchForm.year != null ||
        this.searchForm.year === null ||
        this.searchForm.year === undefined
      ) {
        this.$refs.vxeTable.loadColumn(this.columns); //操作a组件
      }
      request(GET_XSGSREPORTTHEME_USERID, METHOD.GET).then((res) => {
        let visibleColumn = this.$refs.vxeTable.getTableColumn();

        this.visibleColumn = visibleColumn;
        if (res.data.data != null) {
          let fullColumn = visibleColumn.fullColumn;
          let collectColumn = visibleColumn.collectColumn;
          res.data.data.columns1.forEach((temp) => {
            let target = fullColumn.filter((item) => temp === item.property)[0];
            if (target) {
              target.visible = false;
            }
          });

          res.data.data.parentColumns1.forEach((temp) => {
            let target = collectColumn.filter(
              (item) => temp === item.property
            )[0];
            if (target) {
              target.visible = false;
              if (
                "zrId" === target.property ||
                "jtzrId" === target.property ||
                "lastYearBusiness" === target.property ||
                "optionListColumns" === target.property ||
                "basicList" === target.property
              ) {
                target.children.forEach((item) => {
                  item.visible = false;
                  if (item.children != null) {
                    item.children.forEach((a) => {
                      a.visible = false;
                    });
                  }
                });
              }
            }
          });
          this.getMyColumns(visibleColumn, records, total);
        } else {
          this.$refs.vxeTable.loadColumn(this.columns); //操作a组件
          this.getMyColumns(visibleColumn, records, total);
        }
      });
    },
    /** 获取我的Columns */
    getMyColumns(visibleColumn, data, total) {
      let tempCulums = this.$store.getters["account/userAuth"][this.TF_FIELD];
      let fullColumn = visibleColumn.fullColumn;
      let collectColumn = visibleColumn.collectColumn;
      //过滤查询需要删除的字段
      let full = this.getfullColumn(fullColumn, tempCulums);
      let colle = this.getfullColumn(collectColumn, tempCulums);
      //删除需要删掉的字段
      this.deleteNotAuthColumns(full, fullColumn);
      this.deleteNotAuthColumns1(colle, collectColumn, full);
      //重新渲染表头
      this.$refs.vxeTable.loadColumn(fullColumn);
      this.$refs.vxeTable.loadColumn(collectColumn);
      console.log("data", data || "无数据渲染");
      this.showHeader = true;
      this.gridOptions.loading = false;
      this.gridOptions.data = data;
      this.gridOptions.pagerConfig.total = parseInt(total);

      // let fullColumn = visibleColumn.fullColumn
      // let collectColumn = visibleColumn.collectColumn
      // request(LIST_XSGSREPORTCOLUMNS, METHOD.POST, {})
      //     .then(res => {
      //         let records = res.data.data
      //         if (records != null && records.length > 0) {
      //             let tempCulums = eval(records[0].field);
      //             // eval(records[0].halfChecked).forEach(item => {
      //             //     tempCulums.push(item)
      //             // })
      //             //过滤查询需要删除的字段
      //             let full = this.getfullColumn(fullColumn, tempCulums)
      //             let colle = this.getfullColumn(collectColumn, tempCulums)
      //             //删除需要删掉的字段
      //             this.deleteNotAuthColumns(full, fullColumn)
      //             this.deleteNotAuthColumns1(colle, collectColumn, full)
      //             //重新渲染表头
      //             this.$refs.vxeTable.loadColumn(fullColumn)
      //             this.$refs.vxeTable.loadColumn(collectColumn)
      //
      //             /** 获取状态机列号
      //              let tableColumn = this.$refs.vxeTable.getTableColumn().tableColumn;
      //              if(tableColumn.length>0){
      // 					tableColumn.forEach((item,index)=>{
      // 						if(item.property==="platformName"){
      // 							console.log(index)
      // 						}
      // 					})
      // 				}*/
      //
      //             this.showHeader = true
      //             this.gridOptions.loading = false
      //             this.gridOptions.data = data
      //             this.gridOptions.pagerConfig.total = parseInt(total)
      //         }
      //     })
    },
    //删除操作
    deleteNotAuthColumns(columns, deleteColumn) {
      columns.forEach((temp) => {
        let index = deleteColumn.findIndex((item) => item.property === temp);
        if (index > -1) {
          deleteColumn.splice(index, 1);
        }
      });
    },
    deleteNotAuthColumns1(columns, deleteColumn, full) {
      if (columns.length > 0) {
        columns.forEach((temp) => {
          let index = deleteColumn.findIndex((item) => item.property === temp);

          if (index > -1) {
            deleteColumn.splice(index, 1);
          }
        });
        deleteColumn.forEach((item) => {
          if (item.children) {
            if (item.children.length > 0) {
              if (
                item.property != "jtzrId" &&
                item.property != "zrId" &&
                item.property != "basicList" &&
                item.property != "optionListColumns"
              ) {
                full.forEach((temp) => {
                  let index = item.children.findIndex(
                    (item) => item.property === temp
                  );
                  if (index > -1) {
                    item.children.splice(index, 1);
                  }
                });
              }
            }
          }
        });
      } else {
        deleteColumn.forEach((item) => {
          if (item.children) {
            if (item.children.length > 0) {
              if (
                item.property != "jtzrId" &&
                item.property != "zrId" &&
                item.property != "basicList" &&
                item.property != "optionListColumns"
              ) {
                full.forEach((temp) => {
                  let index = item.children.findIndex(
                    (item) => item.property === temp
                  );
                  if (index > -1) {
                    item.children.splice(index, 1);
                  }
                });
              }
            }
          }
        });
      }
    },
    //获取删除字段
    getfullColumn(visibleColumn, baseAuthColumns) {
      let columns = [];
      let sales = ["yearSale1", "yearSale2", "yearSale3"];
      let customer = this.searchForm.customer;
      visibleColumn.forEach((temp) => {
        if (temp.type != "seq" && temp.title != "操作") {
          let flag = this.hasAuthColumns(temp, baseAuthColumns);
          if (!flag) {
            columns.push(temp.property);
          } else {
            // if (!customer) {
            //     if (sales.includes(temp.property)) {
            //         columns.push(temp.property);
            //     }
            // }
          }
        }
      });
      return columns;
    },
    //判断是否存在该表头
    hasAuthColumns(field, baseAuthColumns) {
      let flag = false;
      baseAuthColumns.forEach((item) => {
        if (field.property === item) {
          flag = true;
        }
      });
      return flag;
    },
    toolbarCustomEvent(params) {
      const visibleColumn = this.$refs.vxeTable.getTableColumn();
      this.visibleColumn = visibleColumn;
      const columns = [];
      const parentColumns = [];
      switch (params.type) {
        case "confirm": {
          visibleColumn.fullColumn.forEach((item) => {
            if (!item.visible) {
              columns.push(item.property);
            }
          });
          visibleColumn.collectColumn.forEach((item) => {
            if (!item.visible) {
              parentColumns.push(item.property);
            }
          });
          request(SUBMIT_XSGSREPORTTHEME, METHOD.POST, {
            columns: columns.toString(),
            parentColumns: parentColumns.toString(),
          }).then(() => {
            this.$message.info("保存成功");
          });
          break;
        }
        case "reset": {
          const columns = [];
          const parentColumns = [];
          request(SUBMIT_XSGSREPORTTHEME, METHOD.POST, {
            columns: columns.toString(),
            parentColumns: parentColumns.toString(),
          }).then(() => {
            this.$message.info("保存成功");
          });
          break;
        }
        // case 'close': {
        //     console.log({ message: `关闭了面板，显示为 ${visibleColumn.fullColumn.length} 列`, status: 'info' })
        //   break
        // }
      }
    },
    onclick(record) {
      this.platformName1 = record.platformName;
      this.records = record;
      this.lookAllDialog = true;
      //异步执行的原因，子组件还没渲染完就调用,需要加上 this.$nextTick
      this.$nextTick(() => {
        this.$refs.selectAllOptionalList.getAllData();
      });
    },
    cellClickEvent(e) {
      console.log(e);
    },
    rowStyle(rowObj) {
      let data = rowObj.row;
      if (data.isCheck == 1) {
        return { backgroundColor: "#ffff00" };
      }
    },
    cellStyle(row) {
      //判断折让是否有效
      let pr = row.column.property;
      if (pr != null) {
        if (row.column.property.indexOf("_seqNumber") != -1) {
          let pro = pr.replace("_seqNumber", "_istimeout");
          let value = row.row[pro];
          // console.log(row.column.property + ":" + value);
          if (value && value == "X") {
            return { color: "#f0f" };
          }
        }
      }
      if (row.column.property == "reducePrice") {
        if (row.row.reducePrice && row.row.reducePrice < 0) {
          return { color: "red" };
        } else {
          return { color: "" };
        }
      }
      if (row.column.property == "optionReducePrice") {
        if (row.row.optionReducePrice && row.row.optionReducePrice < 0) {
          return { color: "red" };
        } else {
          return { color: "" };
        }
      }
      if (row.column.property == "optionReducePrice") {
        if (row.row.zbBudgetSpace && row.row.zbBudgetSpace < 0) {
          return { color: "red" };
        } else {
          return { color: "" };
        }
      }
      if (row.column.property == "zbBasicBalance") {
        if (row.row.zbBasicBalance && row.row.zbBasicBalance < 0) {
          return { color: "red" };
        } else {
          return { color: "" };
        }
      }
      if (row.column.property == "zbUnitBalance") {
        if (row.row.zbUnitBalance && row.row.zbUnitBalance < 0) {
          return { color: "red" };
        } else {
          return { color: "" };
        }
      }
      if (row.column.property == "sysBudgetSpace") {
        if (row.row.sysBudgetSpace && row.row.sysBudgetSpace < 0) {
          return { color: "red" };
        } else {
          return { color: "" };
        }
      }
      if (row.column.property == "sysBasicBalance") {
        if (row.row.sysBasicBalance && row.row.sysBasicBalance < 0) {
          return { color: "red" };
        } else {
          return { color: "" };
        }
      }
      if (row.column.property == "sysUnitBalance") {
        if (row.row.sysUnitBalance && row.row.sysUnitBalance < 0) {
          return { color: "red" };
        } else {
          return { color: "" };
        }
      }
      if (row.column.property == "optionAmount") {
        if (row.row.optionAmount && row.row.optionAmount < 0) {
          return { color: "red" };
        } else {
          return { color: "" };
        }
      }
    },
    colspanMethod({ row, _rowIndex, column, visibleData }) {
      const fields = ["platformName", "yearSale1", "yearSale2", "yearSale3"];
      if (!fields.includes(column.property)) {
        return;
      }
      const cellValue = row[column.property];
      //年度
      const yearValue = row["year"];
      //客户号
      const customerValue = row["customer"];
      //状态机
      const platformName = row["platformName"];

      if (column.property == "platformName") {
        if (cellValue) {
          let prevRow = visibleData[_rowIndex - 1];
          let nextRow = visibleData[_rowIndex + 1];
          if (
            prevRow &&
            prevRow[column.property] === cellValue &&
            prevRow["year"] === yearValue &&
            prevRow["customer"] === customerValue
          ) {
            return { rowspan: 0, colspan: 0 };
          } else {
            let countRowspan = 1;
            while (
              nextRow &&
              nextRow[column.property] === cellValue &&
              nextRow["year"] === yearValue &&
              nextRow["customer"] === customerValue
            ) {
              nextRow = visibleData[++countRowspan + _rowIndex];
            }
            if (countRowspan > 1) {
              return { rowspan: countRowspan, colspan: 1 };
            }
          }
        }
      } else {
        // console.log(column.property);
        let prevRow = visibleData[_rowIndex - 1];
        let nextRow = visibleData[_rowIndex + 1];
        if (
          prevRow &&
          prevRow["year"] === yearValue &&
          prevRow["customer"] === customerValue &&
          prevRow["platformName"] === platformName
        ) {
          return { rowspan: 0, colspan: 0 };
        } else {
          let countRowspan = 1;
          while (
            nextRow &&
            nextRow["year"] === yearValue &&
            nextRow["customer"] === customerValue &&
            nextRow["platformName"] === platformName
          ) {
            nextRow = visibleData[++countRowspan + _rowIndex];
          }
          if (countRowspan > 1) {
            return { rowspan: countRowspan, colspan: 1 };
          }
        }
      }
    },
    partsNames() {
      request(PARTS_NAMES, METHOD.GET, {}).then((res) => {
        res.data.forEach((item) => {
          let width = 50;
          if (item.partsName.length > 3) {
            width = 60;
          }
          if (item.partsName.length > 5) {
            width = 70;
          }
          if (item.partsName.length > 8) {
            width = 80;
          }
          if (item.partsName.length > 10) {
            width = 120;
          }
          if (item.subType == "选型件") {
            this.xxColumns.push({
              title: item.partsName,
              field: item.subType + item.partsName,
              width: width,
              align: "center",
              headerClassName: "col-xx",
            });
          } else if (item.subType == "选装件") {
            this.xzColumns.push({
              title: item.partsName,
              field: item.subType + item.partsName,
              width: width,
              align: "center",
              headerClassName: "col-xz",
            });
          }
        });

        this.columns.forEach((item) => {
          if (item.field == "basicList") {
            item.children = this.xxColumns;
          } else if (item.field == "optionListColumns") {
            item.children = this.xzColumns;
          }
        });
      });
    },
    yearchanged() {
      // 创建标题映射配置，减少重复代码
      const titleMappings = {
        lastYearTitle: {
          withYear: {
            synBasicTicketPrice: (y) => `${y - 1}基本型开票价`,
            synUnitTicketPrice: (y) => `${y - 1}整机开票价`,
            synBasicNetPrice: (y) => `${y - 1}基本型实际价格`,
            synUnitNetPrice: (y) => `${y - 1}整机实际价格`,
            synOptionAmount: (y) => `${y - 1}选配件合计`,
            synSettlementPrice: (y) => `${y - 1}结算价`,
            synBalanceAmount: (y) => `${y}单台折让合计`,
            synNetPriceRate: (y) => `${y - 1}年实际价格边贡率`,
          },
          withoutYear: {
            synBasicTicketPrice: "基本型开票价",
            synUnitTicketPrice: "整机开票价",
            synBasicNetPrice: "基本型实际价格",
            synUnitNetPrice: "整机实际价格",
            synOptionAmount: "选配件合计",
            synBalanceAmount: "单台折让合计",
          },
        },
        thisYearTitle: {
          withYear: {
            basicTicketPrice: (y) => `${y}基本型开票价`,
            unitTicketPrice: (y) => `${y}整机开票价`,
            basicNetPrice: (y) => `${y}基本型实际价格`,
            unitNetPrice: (y) => `${y}整机实际价格`,
            settlementPrice: (y) => `${y}结算价`,
            budgetNetPriceCmr: (y) => `${y}实际价格边贡率`,
          },
          withoutYear: {
            basicTicketPrice: "基本型开票价",
            unitTicketPrice: "整机开票价",
            basicNetPrice: "基本型实际价格",
            unitNetPrice: "整机实际价格",
          },
        },
        col_0: {
          withYear: {
            yearSale1: (y) => `${y - 2}年销量`,
            yearSale2: (y) => `${y - 1}年销量`,
            yearSale3: (y) => `${y}年销量`,
          },
          withoutYear: {
            yearSale1: "前年销量",
            yearSale2: "去年销量",
            yearSale3: "今年销量",
          },
        },
        salesVolume: {
          withYear: {
            yearSale1: (y) => `${y - 2}年销量`,
            yearSale2: (y) => `${y - 1}年销量`,
            yearSale3: (y) => `${y}年销量`,
            yearSale4: (y) => `${y}年预算销量`,
            yearSale5: (y) => `${y}年预判销量`,
            yearSale6: (y) => `${y}年销量差额`,
          },
          withoutYear: {
            yearSale1: "前年销量",
            yearSale2: "去年销量",
            yearSale3: "今年销量",
            yearSale4: "今年预算销量",
            yearSale5: "今年预判销量",
            yearSale6: "今年销量差额",
          },
        },
        wholeMachineCost: {
          withYear: {
            basicTicketPrice: (y) => `${y - 1}基本型开票价`,
            yearCost1: (y) => `${y - 1}基准成本`,
            yearCost2: (y) => `${y - 1}实际成本`,
            yearCost3: (y) => `${y}基准成本`,
            yearCost4: (y) => `${y}实际成本`,
          },
          withoutYear: {
            yearCost1: "去年基准成本",
            yearCost2: "去年实际成本",
            yearCost3: "今年基准成本",
            yearCost4: "今年实际成本",
          },
        },
        income: {
          withYear: {
            lastYearNetIncome: (y) => `${y - 1}年净收入(万元)`,
            budgetedNetIncome: (y) => `${y}年预算净收入(万元)`,
            actualPriceRevenue: (y) => `${y}年实际价格收入(万元)`,
            actualPromoAmount: (y) => `${y}年实际推广金额(万元)`,
            actualNetIncome: (y) => `${y}年实际净收入(万元)`,
            revenueVariance: (y) => `${y}年收入差额(万元)`,
          },
          withoutYear: {
            lastYearNetIncome: "去年净收入(万元)",
            budgetedNetIncome: "今年预算净收入(万元)",
            actualPriceRevenue: "今年实际价格收入(万元)",
            actualPromoAmount: "今年实际推广金额(万元)",
            actualNetIncome: "今年实际净收入(万元)",
            revenueVariance: "今年收入差额(万元)",
          },
        },
        lastYearBusiness: {
          withYear: {
            yearHostFactoryPromotion1: (y) => `${y - 1}主机厂推广1`,
            yearUnitNetPrice: (y) => `${y - 1}整机净价`,
            yearUnitNetPriceRate: (y) => `${y - 1}整机净价边贡率`,
          },
          withoutYear: {
            yearHostFactoryPromotion1: "主机厂推广1",
            yearUnitNetPrice: "整机净价",
            yearUnitNetPriceRate: "整机净价边贡率",
          },
        },
        yearTitle: {
          withYear: {
            balanceAmount: (y) => `${y}单台折让合计`,
            yearHostFactoryPromotion1: (y) => `${y}主机厂推广1`,
            yearUnitNetPrice: (y) => `${y}整机净价`,
            yearUnitNetPriceRate: (y) => `${y}整机净价边贡率`,
            unitPromoNoBundle: (y) => `${y}单台加权平均推广(不含打包)`,
            unitPromoAllocBundle: (y) => `${y}单台推广分摊（打包分摊）`,
            unitPromoAllocTotal: (y) => `${y}单台推广分摊合计`,
            finalUnitNetPrice: (y) => `${y}分摊后整机净价`,
            budgetPriceReduction: (y) => `${y}预算价格降价`,
            baseModelBudgetPrice: (y) => `${y}基本型预算价格`,
            currentYearBudgetPrice: (y) => `${y}预算价格`,
            budgetOemPromotion: (y) => `${y}预算主机厂推广`,
            budgetNetPrice: (y) => `${y}预算净价`,
            unitBudgetPolicyTotal: (y) => `${y}单台预算政策合计`,
            budgetPriceSurplus: (y) => `${y}预算价格结余`,
            budgetNetPriceCmr: (y) => `${y}预算净价边贡率`,
          },
          withoutYear: {
            balanceAmount: "单台折让合计",
            yearHostFactoryPromotion1: "主机厂推广1",
            yearUnitNetPrice: "整机净价",
            yearUnitNetPriceRate: "整机净价边贡率",
            unitPromoNoBundle: "单台加权平均推广(不含打包)",
            unitPromoAllocBundle: "单台推广分摊（打包分摊）",
            unitPromoAllocTotal: "单台推广分摊合计",
            finalUnitNetPrice: "分摊后整机净价",
            budgetPriceReduction: "预算价格降价",
            baseModelBudgetPrice: "基本型预算价格",
            currentYearBudgetPrice: "预算价格",
            budgetOemPromotion: "预算主机厂推广",
            budgetNetPrice: "预算净价",
            unitBudgetPolicyTotal: "单台预算政策合计",
            budgetPriceSurplus: "预算价格结余",
            budgetNetPriceCmr: "预算净价边贡率",
          },
        },
      };

      const hasYear = !!this.searchForm.year;
      const currentYear = this.searchForm.year;

      // 统一处理列标题更新，消除嵌套if-else
      this.columns.forEach((column) => {
        const mapping = titleMappings[column.field];
        if (!mapping) return;

        const fieldMappings = hasYear ? mapping.withYear : mapping.withoutYear;
        column.children.forEach((child) => {
          const title = fieldMappings[child.field];
          if (title) {
            child.title =
              typeof title === "function" ? title(currentYear) : title;
          }
        });
      });
    },
    setItemDatas(records) {
      this.prev_no_jts = [];
      this.prev_jts = [];
      this.no_jts = [];
      this.jts = [];
      this.xxColumns = [];
      this.xzColumns = [];

      if (records == null) {
        return;
      }
      let details = [];
      let preDetails = [];
      records.forEach((item) => {
        if (item.setDetails != null && item.setDetails.length > 0) {
          details.push(...item.setDetails);
        }
        if (
          item.prevYearSetDetails != null &&
          item.prevYearSetDetails.length > 0
        ) {
          preDetails.push(...item.prevYearSetDetails);
        }
      });
      if (details != null) {
        details.sort((o1, o2) => {
          let v1 = o1.seqNum ? Number(o1.seqNum) : 0;
          let v2 = o2.seqNum ? Number(o2.seqNum) : 0;
          if (v1 < v2) {
            return -1;
          } else if (v1 > v2) {
            return 1;
          } else {
            return 0;
          }
        });
        details.forEach((key) => {
          if ("阶梯金额" != key.allowanceType) {
            if (!this.isExtis("no_jts", "ni_" + key.seqNum + "_seqNumber")) {
              this.no_jts.push({
                title: key.seqName,
                field: "ni_" + key.seqNum + "_seqNumber",
                width: 70,
                align: "center",
                headerClassName: "col-fjt",
              });
              this.no_jts.push({
                title: "类型",
                field: "ni_" + key.seqNum + "_allowanceType",
                width: 70,
                align: "center",
                headerClassName: "col-fjt",
              });
              this.no_jts.push({
                title: "备注",
                field: "ni_" + key.seqNum + "_remarks",
                width: 70,
                align: "center",
                slots: { default: "defaultRemarks" },
                headerClassName: "col-fjt",
                ellipsis: true,
              });
            }
          } else {
            if (key.allowanceType == "阶梯金额") {
              if (key.jtList != null) {
                key.jtList.forEach((i) => {
                  if (!this.isExtis("jts", "i_" + i.seqNumber + "_seqNumber")) {
                    this.jts.push({
                      title: i.seqName,
                      field: "i_" + i.seqNumber + "_seqNumber",
                      width: 70,
                      align: "center",
                      headerClassName: "col-jt",
                      ellipsis: true,
                    });
                  }
                });
              }
            }
          }
        });
      }
      if (preDetails != null) {
        preDetails.sort((o1, o2) => {
          let v1 = o1.seqNum ? Number(o1.seqNum) : 0;
          let v2 = o2.seqNum ? Number(o2.seqNum) : 0;
          if (v1 < v2) {
            return -1;
          } else if (v1 > v2) {
            return 1;
          } else {
            return 0;
          }
        });
        preDetails.forEach((key) => {
          console.log("修改", key.allowanceType);
          if ("阶梯金额" != key.allowanceType) {
            if (
              !this.isExtis(
                "prev_no_jts",
                "prevYear_ni_" + key.seqNum + "_seqNumber"
              )
            ) {
              this.prev_no_jts.push({
                title: key.seqName,
                field: "prevYear_ni_" + key.seqNum + "_seqNumber",
                width: 70,
                align: "center",
                headerClassName: "col-pre-fjt",
              });
              this.prev_no_jts.push({
                title: "类型",
                field: "prevYear_ni_" + key.seqNum + "_allowanceType",
                width: 70,
                align: "center",
                headerClassName: "col-pre-fjt",
                ellipsis: true,
              });
              this.prev_no_jts.push({
                title: "备注",
                field: "prevYear_ni_" + key.seqNum + "_remarks",
                width: 70,
                align: "center",
                headerClassName: "col-pre-fjt",
                slots: { default: "defaultRemarks" },
                ellipsis: true,
              });
            }
          } else {
            if (key.allowanceType == "阶梯金额") {
              if (key.jtList != null) {
                key.jtList.forEach((i) => {
                  if (
                    !this.isExtis(
                      "prev_jts",
                      "prevYear_i_" + i.seqNumber + "_seqNumber"
                    )
                  ) {
                    this.prev_jts.push({
                      title: i.seqName,
                      field: "prevYear_i_" + i.seqNumber + "_seqNumber",
                      width: 70,
                      align: "center",
                      headerClassName: "col-pre-fjt",
                      ellipsis: true,
                    });
                  }
                });
              }
            }
          }
        });
      }

      records.forEach((item) => {
        if (item.setDetails != null) {
          item.setDetails.forEach((key) => {
            if ("阶梯金额" != key.allowanceType) {
              // if (!this.isExtis("no_jts", 'ni_' + key.seqNum + '_seqNumber')) {
              //     this.no_jts.push({
              //         title: key.seqName,
              //         field: 'ni_' + key.seqNum + '_seqNumber',
              //         width: 80,
              //         align: 'center',
              //         headerClassName: 'col-fjt'
              //     })
              //     this.no_jts.push({
              //         title: "类型",
              //         field: 'ni_' + key.seqNum + '_allowanceType',
              //         width: 80,
              //         align: 'center',
              //         headerClassName: 'col-fjt'
              //     })
              //     this.no_jts.push({
              //         title: "备注",
              //         field: 'ni_' + key.seqNum + '_remarks',
              //         width: 80,
              //         align: 'center',
              //         headerClassName: 'col-fjt',
              //         ellipsis: true
              //     })
              // }
              //判断小数点保留方式
              let isSave =
                key.saveType === "保留两位(多位四舍五入)" ||
                key.saveType === "保留两位(两位四舍五入)";
              item["ni_" + key.seqNum + "_seqNumber"] =
                key.allowanceType === "百分比" && isSave
                  ? parseFloat(key.allowanceAmount).toFixed(2)
                  : key.allowanceAmount;
              item["ni_" + key.seqNum + "_istimeout"] = key.timeoutFlag;

              let remarks = key.remarks;
              if (remarks == null) {
                remarks = "";
              }

              if (key.defaultRemarks) {
                item["ni_" + key.seqNum + "_remarks"] =
                  (key.beginTime
                    ? "生效时间：" +
                      key.beginTime +
                      "~" +
                      key.endTime +
                      "\n————————————————\n"
                    : "") + key.defaultRemarks;
              } else {
                item["ni_" + key.seqNum + "_remarks"] =
                  (key.beginTime
                    ? "生效时间：" +
                      key.beginTime +
                      "~" +
                      key.endTime +
                      "\n————————————————\n"
                    : "") + remarks;
              }
              item["ni_" + key.seqNum + "_allowanceType"] = key.allowanceType;
            } else {
              if (key.allowanceType == "阶梯金额") {
                if (key.jtList != null) {
                  key.jtList.forEach((i) => {
                    // if (!this.isExtis("jts", 'i_' + i.seqNumber + '_seqNumber')) {
                    //     this.jts.push({
                    //         title: i.seqName,
                    //         field: 'i_' + i.seqNumber + '_seqNumber',
                    //         width: 80,
                    //         align: 'center',
                    //         headerClassName: 'col-jt'
                    //     })
                    // }
                    item["i_" + i.seqNumber + "_seqNumber"] = i.price;
                    item["i_" + i.seqNumber + "_istimeout"] = key.timeoutFlag;
                  });

                  let remarks = key.remarks;
                  if (remarks == null) {
                    remarks = "";
                  }
                  if (key.defaultRemarks) {
                    item["jtzr_remarks"] =
                      (key.beginTime
                        ? "生效时间：" +
                          key.beginTime +
                          "~" +
                          key.endTime +
                          "\n————————————————\n"
                        : "") +
                      key.defaultRemarks +
                      remarks;
                  } else {
                    item["jtzr_remarks"] =
                      (key.beginTime
                        ? "生效时间：" +
                          key.beginTime +
                          "~" +
                          key.endTime +
                          "\n————————————————\n"
                        : "") + remarks;
                  }
                }
              }
            }
          });
        }

        if (item.prevYearSetDetails != null) {
          item.prevYearSetDetails.forEach((key) => {
            if ("阶梯金额" != key.allowanceType) {
              // if (!this.isExtis("prev_no_jts", 'prevYear_ni_' + key.seqNum + '_seqNumber')) {
              //     this.prev_no_jts.push({
              //         title: key.seqName,
              //         field: 'prevYear_ni_' + key.seqNum + '_seqNumber',
              //         width: 80,
              //         align: 'center',
              //         headerClassName: 'col-fjt'
              //     })
              //     this.prev_no_jts.push({
              //         title: "类型",
              //         field: 'prevYear_ni_' + key.seqNum + '_allowanceType',
              //         width: 80,
              //         align: 'center',
              //         headerClassName: 'col-fjt'
              //     })
              //     this.prev_no_jts.push({
              //         title: "备注",
              //         field: 'prevYear_ni_' + key.seqNum + '_remarks',
              //         width: 80,
              //         align: 'center',
              //         headerClassName: 'col-fjt',
              //         ellipsis: true
              //     })
              // }
              item["prevYear_ni_" + key.seqNum + "_seqNumber"] =
                key.allowanceAmount;
              item["prevYear_ni_" + key.seqNum + "_istimeout"] =
                key.timeoutFlag;
              let remarks = key.remarks;
              if (remarks == null) {
                remarks = "";
              }

              if (key.defaultRemarks) {
                item["prevYear_ni_" + key.seqNum + "_remarks"] =
                  (key.beginTime
                    ? "生效时间：" +
                      key.beginTime +
                      "~" +
                      key.endTime +
                      "\n————————————————\n"
                    : "") + key.defaultRemarks;
              } else {
                item["prevYear_ni_" + key.seqNum + "_remarks"] =
                  (key.beginTime
                    ? "生效时间：" +
                      key.beginTime +
                      "~" +
                      key.endTime +
                      "\n————————————————\n"
                    : "") + remarks;
              }
              item["prevYear_ni_" + key.seqNum + "_allowanceType"] =
                key.allowanceType;
            } else {
              if (key.allowanceType == "阶梯金额") {
                if (key.jtList != null) {
                  key.jtList.forEach((i) => {
                    // if (!this.isExtis("prev_jts", 'prevYear_i_' + i.seqNumber + '_seqNumber')) {
                    //     this.prev_jts.push({
                    //         title: i.seqName,
                    //         field: 'prevYear_i_' + i.seqNumber + '_seqNumber',
                    //         width: 80,
                    //         align: 'center',
                    //         headerClassName: 'col-jt'
                    //     })
                    // }
                    item["prevYear_i_" + i.seqNumber + "_seqNumber"] = i.price;
                    item["prevYear_i_" + i.seqNumber + "_istimeout"] =
                      key.timeoutFlag;
                  });
                  let remarks = key.remarks;
                  if (remarks == null) {
                    remarks = "";
                  }
                  if (key.defaultRemarks) {
                    item["prevYear_jtzr_remarks"] =
                      (key.beginTime
                        ? "生效时间：" +
                          key.beginTime +
                          "~" +
                          key.endTime +
                          "\n————————————————\n"
                        : "") +
                      key.defaultRemarks +
                      remarks;
                  } else {
                    item["prevYear_jtzr_remarks"] =
                      (key.beginTime
                        ? "生效时间：" +
                          key.beginTime +
                          "~" +
                          key.endTime +
                          "\n————————————————\n"
                        : "") + remarks;
                  }
                }
              }
            }
          });
        }

        if (item.allowanceParts != null) {
          item.allowanceParts.forEach((key) => {
            // 要区别选型 选装
            if (key.subType === "选型件") {
              if (key.isSubtraction === "0") {
                if (key.valuePrice === 0) {
                  if (key.specs === key.standardSpecs) {
                    //原来逻辑是默认为0
                    // item[key.subType+key.partsName] = key.valuePrice
                    //现在改为空值
                    item[key.subType + key.partsName] = null;
                  } else {
                    item[key.subType + key.partsName] = key.specs;
                  }
                } else {
                  item[key.subType + key.partsName] = key.valuePrice;
                }
              } else if (key.isSubtraction === "1") {
                if (key.actualPrice === 0) {
                  item[key.subType + key.partsName] = "不带";
                } else {
                  item[key.subType + key.partsName] = key.actualPrice;
                }
              }
            } else if (key.subType === "选装件") {
              item[key.subType + key.partsName] = key.actualPrice;
            }
          });
        }
      });

      this.jts.push({
        title: "备注",
        field: "jtzr_remarks",
        width: 80,
        align: "center",
        headerClassName: "col-jt",
        ellipsis: true,
      });
      if (this.no_jts.length == 0) {
        this.no_jts.push({
          title: "",
          field: "ni_jtzr_remarks",
          width: 80,
          align: "center",
          headerClassName: "col-fjt",
        });
      }

      this.prev_jts.push({
        title: "备注",
        field: "prevYear_jtzr_remarks",
        width: 80,
        align: "center",
        headerClassName: "col-pre-fjt",
        ellipsis: true,
      });
      if (this.prev_no_jts.length == 0) {
        this.prev_no_jts.push({
          title: "",
          field: "prevYear_ni_jtzr_remarks",
          width: 80,
          align: "center",
          headerClassName: "col-pre-fjt",
          ellipsis: true,
        });
      }

      if (this.xxColumns.length == 0) {
        this.xxColumns.push({
          title: "",
          field: "xxColumns",
          width: 70,
          align: "center",
          headerClassName: "col-fjt",
        });
      }
      if (this.xzColumns.length == 0) {
        this.xzColumns.push({
          title: "",
          field: "xzColumns",
          width: 70,
          align: "center",
          headerClassName: "col-fjt",
        });
      }

      this.columns.forEach((item) => {
        if (item.field == "yearTitle") {
          item.children.forEach((row) => {
            if (row.field == "jtzrId") {
              row.children = [...this.jts];
            } else if (row.field == "zrId") {
              row.children = [...this.no_jts];
            }
          });
        } else if (item.field == "lastYearBusiness") {
          item.children.forEach((row) => {
            if (row.field == "prevZrId") {
              row.children = [...this.prev_no_jts];
            } else if (row.field == "prevJtZrId") {
              row.children = [...this.prev_jts];
            }
          });
        }
      });
    },
    isExtis(list, key) {
      let flag = false;
      if ("no_jts" == list) {
        this.no_jts.forEach((item) => {
          if (item.field == key) {
            flag = true;
          }
        });
      } else if ("jts" == list) {
        this.jts.forEach((item) => {
          if (item.field == key) {
            flag = true;
          }
        });
      } else if ("prev_no_jts" == list) {
        this.prev_no_jts.forEach((item) => {
          if (item.field == key) {
            flag = true;
          }
        });
      } else if ("prev_jts" == list) {
        this.prev_jts.forEach((item) => {
          if (item.field == key) {
            flag = true;
          }
        });
      }

      return flag;
    },
    compare(o1, o2, field) {
      let v1 = o1[field] ? Number(o1[field]) : 0;
      let v2 = o2[field] ? Number(o2[field]) : 0;
      if (v1 < v2) {
        return -1;
      } else if (v1 > v2) {
        return 1;
      } else {
        return 0;
      }
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },

    //关闭清单对话框
    handlelookAllSubmit() {
      this.lookAllDialog = false;
    },
    //关闭清单对话框
    closelookAllForm() {
      this.lookAllDialog = false;
    },

    /** 处理查询 */
    handleSearch() {
      this.gridOptions.pagerConfig.currentPage = 1;
      this.jts = [];
      this.no_jts = [];
      this.showHeader = false;
      this.gridOptions.data = [];
      this.getData();
      this.getTgxxData();
      this.getTgsxxData();
    },
    /** 重置按钮操作 */
    resetQuery() {
      (this.productModel = []),
        (this.machineType = []),
        (this.blowoff = []),
        (this.plate = []),
        (this.series = []),
        (this.ps = []),
        (this.type = []),
        (this.searchForm = {
          customerName: null,
          customer: null,
        });
      var aData = new Date();
      this.searchForm.year = Number(this.$moment(aData).format("YYYY"));
      this.handleSearch();
    },
    /** 处理分页 */
    handlePageChange({ currentPage, pageSize }) {
      this.gridOptions.pagerConfig.currentPage = currentPage;
      this.gridOptions.pagerConfig.pageSize = pageSize;
      this.showHeader = false;
      this.gridOptions.data = [];
      this.getData();
    },

    handlePageChangeTG({ currentPage, pageSize }) {
      this.gridOptionsTG.pagerConfig.currentPage = currentPage;
      this.gridOptionsTG.pagerConfig.pageSize = pageSize;
      this.getTgxxData();
    },
    handlePageChangeTGS({ currentPage, pageSize }) {
      this.gridOptionsTGS.pagerConfig.currentPage = currentPage;
      this.gridOptionsTGS.pagerConfig.pageSize = pageSize;
      this.getTgsxxData();
    },
    /** 编辑，打开对话框 */
    handleEdit(record) {
      let query = {
        option: 1,
        parentPath: this.fullPath,
      };
      this.$router.push({
        path: "/xsgs_allowance_main/" + record.masterId,
        props: true,
        query: { ...query },
      });
    } /** 编辑，打开对话框 */,
    /** 删除单行数据 */
    handleDeleteRow(row) {
      // row.id ,调用接口，传递 row.id 删除当前行数据
      this.$confirm({
        content: "确认删除该条数据吗？",
        title: "提示",
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        onOk: () => {
          console.log("删除的id", row.id);
          request(`${DETAIL_XSGSALLOWANCEITEM}/${row.id}`, METHOD.DELETE, {})
            .then((res) => {
              this.$message.success("删除成功");
              this.handleSearch();
            })
            .catch((err) => {
              console.error(err);
              this.$message.error("删除失败");
            });
        },
        onCancel: () => {
          this.destroyAll();
        },
      });
    },
    handleCompare(record) {
      this.$router.push({
        path: "/xsgs_allowance_compare/" + record.id,
        props: true,
      });
    },
    /** 参考，打开对话框 */
    refEdit(record) {
      let query = {
        option: 0,
        parentPath: this.fullPath,
        reference: record.machineType,
        plate: record.plate,
        customer: record.customer,
        customerName: record.customerName,
      };
      this.$router.push({
        path: "/xsgs_allowance_main/:id",
        props: true,
        query: { ...query },
      });
    },
    getTgxxData: function () {
      this.gridOptionsTG.loading = true;
      let current = this.gridOptionsTG.pagerConfig.currentPage;
      let size = this.gridOptionsTG.pagerConfig.pageSize;
      this.searchForm.productModels = this.productModel.join(",");
      this.searchForm.machineTypes = this.machineType.join(",");
      this.searchForm.blowoffs = this.blowoff.join(",");
      this.searchForm.plates = this.plate.join(",");
      this.searchForm.seriess = this.series.join(",");
      this.searchForm.pss = this.ps.join(",");
      this.searchForm.types = this.type.join(",");
      request(LIST_XSGSFEEPROMOTE_PAGE, METHOD.GET, {
        ...this.searchForm,
        current,
        size,
      }).then((res) => {
        const { records, total } = res.data.data;
        this.gridOptionsTG.loading = false;
        this.gridOptionsTG.data = records;
        this.gridOptionsTG.pagerConfig.total = parseInt(total);
      });
    },
    getTgsxxData: function () {
      this.gridOptionsTGS.loading = true;
      let current = this.gridOptionsTGS.pagerConfig.currentPage;
      let size = this.gridOptionsTGS.pagerConfig.pageSize;
      this.searchForms.year = this.searchForm.year
        ? this.searchForm.year - 1
        : undefined;
      this.searchForms.productModels = this.productModel.join(",");
      this.searchForms.machineTypes = this.machineType.join(",");
      this.searchForms.blowoffs = this.blowoff.join(",");
      this.searchForms.plates = this.plate.join(",");
      this.searchForms.seriess = this.series.join(",");
      this.searchForms.pss = this.ps.join(",");
      this.searchForms.types = this.type.join(",");
      request(LIST_XSGSFEEPROMOTE_PAGE, METHOD.GET, {
        ...this.searchForms,
        current,
        size,
      }).then((res) => {
        const { records, total } = res.data.data;
        this.gridOptionsTGS.loading = false;
        this.gridOptionsTGS.data = records;
        this.gridOptionsTGS.pagerConfig.total = parseInt(total);
      });
    },
    cellContextMenuEvent({ row }) {},
    contextMenuClickEvent({ menu, row, column }) {
      console.log(menu);
      switch (menu.code) {
        case "edit":
          if (row && column) {
            this.handleEdit(row);
          }
          break;
        case "delete":
          if (row && column) {
            this.handleDeleteRow(row);
          }
          break;

        case "copy":
          if (row && column) {
            this.refEdit(row);
          }
          break;
        case "compare":
          if (row && column) {
            this.handleCompare(row);
          }
          break;
      }
    },
    setTableCustomer() {
      let customer = this.searchForm.customer;
      this.customerStr = null;
      let that = this;
      if (customer) {
        request(LIST_MAINSUBCUST, METHOD.GET, { customer: customer }).then(
          (res) => {
            res.data.data.forEach((item) => {
              if (that.customerStr == null) {
                that.customerStr = item.customer + " " + item.name;
              } else {
                that.customerStr += "、" + item.customer + " " + item.name;
              }
            });
          }
        );
      }
    },
    /** 同步导出 **/
    exportDataEvent() {
      let that = this;
      this.$confirm({
        title: "提示",
        content: "是否确认导出所有信息数据?",
        onOk() {
          that.$refs.vxeTable.exportData({ type: "xlsx" });
          setTimeout(() => {
            that.$refs.xTable1.exportData({
              type: "xlsx",
              filename: "本年度推广",
              sheetName: "Sheet1",
            });
          }, 100);
          setTimeout(() => {
            that.$refs.xTable2.exportData({
              type: "xlsx",
              filename: "上一年度推广",
              sheetName: "Sheet1",
            });
          }, 100);
        },
        onCancel() {},
      });
    },
  },
};
</script>

<style scoped>
.ant-input-number {
  width: 100%;
}

.ant-table-thead > tr > th,
.ant-table-tbody > tr > /deep/ td {
  padding: 1px 1px;
  /*overflow-wrap: break-word;*/
}

/*/deep/  样式穿透
*/
/deep/ .ant-col-8 {
  display: block;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
}

/deep/ .ant-form-item {
  margin: 0;
}

/deep/ .ant-form > table > tbody > tr > td {
  border: 1px solid #f0f0f0;
}

/deep/ .vxe-table--render-default .vxe-body--column.col--ellipsis,
.vxe-table--render-default.vxe-editable .vxe-body--column,
.vxe-table--render-default .vxe-footer--column.col--ellipsis,
.vxe-table--render-default .vxe-header--column.col--ellipsis {
  height: 0px;
}

/deep/ .vxe-table--render-default .vxe-body--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-footer--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
  padding: 0px 0;
}

/deep/ .vxe-table--render-default.border--full .vxe-body--column,
/deep/ .vxe-table--render-default.border--full .vxe-footer--column,
/deep/ .vxe-table--render-default.border--full .vxe-header--column {
  background-image: -webkit-gradient(
      linear,
      left top,
      left bottom,
      from(#464646),
      to(#464646)
    ),
    -webkit-gradient(linear, left top, left bottom, from(#464646), to(#464646));
  background-image: linear-gradient(#464646, #464646),
    linear-gradient(#464646, #464646);
  background-repeat: no-repeat;
  background-size: 1px 100%, 100% 1px;
  background-position: 100% 0, 100% 100%;
}

/deep/ .vxe-table--render-default.border--default .vxe-table--header-wrapper,
.vxe-table--render-default.border--full .vxe-table--header-wrapper,
.vxe-table--render-default.border--outer .vxe-table--header-wrapper {
  background-color: #f8f8f9;
}

.vxe-table--render-default .vxe-body--column:not(.col--ellipsis),
.vxe-table--render-default .vxe-footer--column:not(.col--ellipsis),
/deep/ .vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
  padding: 0px 0;
}

/deep/
  .vxe-table--render-default
  .vxe-table--header-wrapper
  .vxe-table--header
  .vxe-header--row
  th
  .vxe-cell {
  padding: 0px 0;
}

/deep/
  .vxe-table--render-default
  .vxe-table--body-wrapper
  .vxe-table--body
  .vxe-body--column
  .vxe-cell {
  padding: 0px 0;
}

/deep/ .vxe-pager.is--perfect {
  border: 0px solid #464646;
  border-top-width: 0;
  background-color: #fff;
}

/deep/ .vxe-table--render-default .vxe-cell {
  white-space: pre-line;
  word-break: normal;
  padding-left: 10px;
  padding-right: 10px;
}

/deep/ .vxe-header--column {
  background-color: rgba(0, 0, 0, 0.2);
}

/deep/ .vxe-table--render-default .vxe-body--row.row--stripe {
  background-color: #e6fdff;
}

/deep/ .col-blue {
  background-color: #e5f8ff;
}

/deep/ .col-lastYear {
  background-color: #fff9e5;
}
/deep/ .col-pre-jt {
  background-color: #fff9e5;
}

/deep/ .col-pre-fjt {
  background-color: #fff9e5;
}

/deep/ .col-basic {
  background-color: #f2f2f2;
}
/deep/ .col-zz {
  background-color: #d9ffec;
}

/deep/ .col-xtb {
  background-color: #d9e5ff;
}

/deep/ .col-bg {
  background-color: #fddbfb;
}

/deep/ .col-jt {
  background-color: #e5f8ff;
}

/deep/ .col-fjt {
  background-color: #e5f8ff;
}

/deep/ .col-xx {
  background-color: #fff0c1;
}
/deep/ .col-xx-h {
  background-color: #f8cbad;
}
/deep/ .col-xz {
  background-color: #fff0c1;
}
/deep/ .col-xz-h {
  background-color: #fff0c1;
}
/deep/ .col-bz {
  background-color: white;
}

/deep/ .vxe-table--render-default .vxe-body--row.row--hover {
  background-color: #fcce10;
}

/deep/ .vxe-table--render-default .vxe-body--row.row--current {
  background-color: #fcce10;
}

/deep/ .vxe-table--render-default .vxe-body--column.col--current {
  background-color: #fcce10;
}

/deep/ .vxe-table--render-default .vxe-header--column.col--current {
  background-color: #fcce10;
}
/deep/ .vxe-header--row {
  height: 22px !important;
}
/deep/ .vxe-header--row > th {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

/deep/ .vxe-table--render-default.border--full .vxe-header--column {
  background-image: none !important;
  border: 1px solid;
}
/deep/ .vxe-table--render-default.border--full .vxe-body--column {
  background-image: none !important;
  border: 1px solid;
}
/deep/ .vxe-table--render-default .vxe-header--column:not(.col--ellipsis) {
  padding: 8px 0;
}
/deep/ .vxe-table--main-wrapper {
  border: 1px solid;
}
/deep/ .vxe-table--fixed-left-wrapper {
  border-left: 1px solid;
  border-top: 1px solid;
}
/deep/ .vxe-table--fixed-right-wrapper {
  border-right: 1px solid;
  border-top: 1px solid;
}
</style>
